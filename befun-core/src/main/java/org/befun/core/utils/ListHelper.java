package org.befun.core.utils;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class ListHelper {

    public static <T> List<T> arrayList(Consumer<Builder<T>> addValues) {
        Builder<T> builder = new Builder<>();
        addValues.accept(builder);
        return builder.list;
    }

    public static <T> List<T> arrayList(T v) {
        return arrayList(i -> i.add(v));
    }

    public static <T> List<T> arrayList(T... v) {
        List<T> list = new ArrayList<>();
        Arrays.stream(v).filter(Objects::nonNull).forEach(list::add);
        return list;
    }

    public static class Builder<T> {
        private final List<T> list = new ArrayList<>();

        public Builder<T> add(T value) {
            if (value != null) {
                list.add(value);
            }
            return this;
        }
    }

    public static <OLD, NEW, KEY> void diff(
            List<OLD> oldList,
            Function<OLD, KEY> comparePropertyOld,
            List<NEW> newList,
            Function<NEW, KEY> comparePropertyNew,
            Consumer<List<NEW>> consumerAdd,
            Consumer<List<OLD>> consumerDelete) {
        diff(oldList, comparePropertyOld, newList, comparePropertyNew, null, consumerAdd, null, consumerDelete);
    }

    public static <OLD, NEW, KEY> void diff(
            List<OLD> oldList,
            Function<OLD, KEY> comparePropertyOld,
            List<NEW> newList,
            Function<NEW, KEY> comparePropertyNew,
            Consumer<List<NEW>> consumerAdd,
            Consumer<Map<NEW, OLD>> consumerUpdate,
            Consumer<List<OLD>> consumerDelete) {
        diff(oldList, comparePropertyOld, newList, comparePropertyNew, null, consumerAdd, consumerUpdate, consumerDelete);
    }

    public static <OLD, NEW, KEY> void diff(
            List<OLD> oldList,
            Function<OLD, KEY> comparePropertyOld,
            List<NEW> newList,
            Function<NEW, KEY> comparePropertyNew,
            BiFunction<OLD, NEW, Boolean> compareValue,
            Consumer<List<NEW>> consumerAdd,
            Consumer<Map<NEW, OLD>> consumerUpdate,
            Consumer<List<OLD>> consumerDelete) {
        if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(newList)) {
            return;
        }
        List<NEW> add = new ArrayList<>();
        Map<NEW, OLD> update = new HashMap<>();
        List<OLD> delete = new ArrayList<>();
        if (CollectionUtils.isEmpty(oldList)) {
            add.addAll(newList);
        } else if (CollectionUtils.isEmpty(newList)) {
            delete.addAll(oldList);
        } else {
            Map<OLD, KEY> originOldMap = new HashMap<>(oldList.size());
            Map<KEY, OLD> oldMap = new HashMap<>(oldList.size());
            oldList.forEach(o -> {
                KEY key = comparePropertyOld.apply(o);
                // 如果有转换出相同的key,则 oldMap 中的元素会被覆盖掉，所以这里多保存一份原始数据的对应关系
                originOldMap.put(o, key);
                oldMap.put(key, o);
            });

            Map<KEY, NEW> newMap = new HashMap<>(newList.size());
            newList.forEach(n -> {
                KEY key = comparePropertyNew.apply(n);
                newMap.put(key, n);
                OLD o = oldMap.get(key);
                if (o != null) {
                    // 如果旧的列表中存在，则比较一下数据内容是否相同
                    if (compareValue != null) {
                        Boolean u = compareValue.apply(o, n);
                        if (u == null || !u) {
                            update.put(n, o);
                        }
                    }
                } else {
                    // 如果旧的列表不存在，则添加到待新增的列表
                    add.add(n);
                }
            });
            originOldMap.forEach((o, key) -> {
                NEW n = newMap.get(key);
                if (n == null) {
                    // 如果新的列表不存在，则添加到待删除的列表
                    delete.add(o);
                }
            });
        }
        if (!add.isEmpty() && consumerAdd != null) {
            consumerAdd.accept(add);
        }
        if (!update.isEmpty() && consumerUpdate != null) {
            consumerUpdate.accept(update);
        }
        if (!delete.isEmpty() && consumerDelete != null) {
            consumerDelete.accept(delete);
        }
    }

    public static List<Long> parseDepartmentIds(String formatDepartmentIds) {
        if (StringUtils.isEmpty(formatDepartmentIds)) {
            return new ArrayList<>();
        }
        return Arrays.stream(formatDepartmentIds
                        .replaceAll("\\[", "")
                        .replaceAll("]", "")
                        .split(","))
                .filter(NumberUtils::isDigits).map(Long::valueOf).collect(Collectors.toList());
    }

    public static String formatDepartmentIds(Collection<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        // [[123],[456],[789]]
        return departmentIds.stream().filter(Objects::nonNull).distinct().sorted().map(i -> "[" + i + "]").collect(Collectors.joining(",", "[", "]"));
    }

    public static Set<Long> getSubId(List<Long> targetIds, List<Long[]> ids) {
        Set<Long> subIds = new HashSet<>();
        for (Long tid : targetIds) {
            ids.stream().filter(d -> d[1].equals(tid)).forEach(d -> {
                subIds.add(d[0]);
                subIds.addAll(getSubId(ListHelper.arrayList(d[0]), ids));
            });
        }
        return subIds;
    }
}
