package org.befun.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.dto.entity.ReferenceFieldDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.FieldRelationType;
import org.befun.core.service.CrudService;
import org.hibernate.annotations.Formula;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.lang.reflect.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.befun.core.rest.context.FieldRelationType.ONE_TO_ONE;

@Slf4j
public class EntityUtility {
    private static Map<Class, Map<String, ReferenceFieldDto>> manyReferenceCache = new HashMap<>();
    private static Map<Class, Map<String, ReferenceFieldDto>> oneReferenceCache = new HashMap<>();
    private static Map<Class, Map<String, Field>> fieldsCache = new HashMap<>();
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static List<Class<?>> NATIVE_TYPES = Arrays.asList(new Class[]{
            String.class,
            int.class,
            Integer.class,
            double.class,
            Double.class,
            Arrays.class,
            List.class,
            Set.class,
            Map.class,
            long.class,
            Long.class,
            boolean.class,
            Boolean.class,
            Date.class,
            BigDecimal.class
    });
    private static List<String> EXINCLUDE_COLUMN = Arrays.asList("id", "createTime", "modifyTime");


    private static List<Field> getEntityFields(Class<? extends BaseEntity> entityClass) {
        List<Field> fields = entityClass.equals(BaseEntity.class)
                ? new ArrayList<>()
                : new ArrayList<>(Arrays.asList(entityClass.getDeclaredFields()));
        if (!entityClass.equals(BaseEntity.class)) {
            fields.addAll(getEntityFields((Class<? extends BaseEntity>) entityClass.getSuperclass()));
        }

        return fields;
    }

    /**
     * clone a entity with relations as well (the ctor way, which requires T has clone ctor)
     *
     * @param object
     * @param <T>
     * @return
     */
    public static <T extends BaseEntity> T clone(T object) throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        Assert.notNull(object, "missing object");

        Constructor<?> cons = object.getClass().getConstructor();
        T newObject = (T) cons.newInstance();

        List<Field> fields = getEntityFields(object.getClass());
        for (Field field : fields) {
            if (field.getAnnotation(Id.class) != null || Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            String name = field.getName();
            Class<?> type = field.getType();
            Object value = PropertyUtils.getProperty(object, name);

            // relation check
            ResourceFieldCloneRule[] cloneRules = field.getAnnotationsByType(ResourceFieldCloneRule.class);
            Enumerated[] ems = field.getAnnotationsByType(Enumerated.class);
            Embedded[] embeddeds = field.getAnnotationsByType(Embedded.class);
            OneToMany[] otms = field.getAnnotationsByType(OneToMany.class);
            ManyToOne[] mtos = field.getAnnotationsByType(ManyToOne.class);
            Transient[] trs = field.getAnnotationsByType(Transient.class);
            Formula[] fms = field.getAnnotationsByType(Formula.class);

            if (fms.length > 0 || trs.length > 0 || (cloneRules.length > 0 && cloneRules[0].ignore())) {
                continue;
            }

            String copyExpression = cloneRules.length > 0 ? cloneRules[0].value() : null;
            if (copyExpression != null) {
                ExpressionParser parser = new SpelExpressionParser();
                Expression exp = parser.parseExpression(copyExpression);
                EvaluationContext context = new StandardEvaluationContext(object);
                value = exp.getValue(context);
            }

            if (ems.length > 0 || embeddeds.length > 0) {
                PropertyUtils.setProperty(newObject, name, value);
            } else if (mtos.length > 0) {
                // many-to-one, skip process, no copy, will be set in one-to-many
            } else if (otms.length > 0) {
                // one-to-many
                Collection<BaseEntity> cloneItems = (Collection<BaseEntity>) PropertyUtils.getProperty(newObject, name);
                Collection<BaseEntity> items = (Collection<BaseEntity>) PropertyUtils.getProperty(object, name);
                if (items != null && items.size() > 0) {
                    for (BaseEntity item : items) {
                        BaseEntity clonedItem = clone(item);
                        // normally many-to-one and one-to-many should be paired, so set by mappedBy
                        PropertyUtils.setProperty(clonedItem, otms[0].mappedBy(), newObject);
                        cloneItems.add(clonedItem);
                    }
                }
            } else if (NATIVE_TYPES.contains(type)) {
                PropertyUtils.setProperty(newObject, name, value);
            } else if (!type.isPrimitive() && !String.class.equals(type)) {
                if (BaseEntity.class.isAssignableFrom(type)) {
                    // 这是一个BaseEntity类型或其子类
                    BaseEntity clonedEntity = (BaseEntity) clone((T) value);  // 递归克隆
                    PropertyUtils.setProperty(newObject, name, clonedEntity);
                } else {
                    // 处理其他类型
                    PropertyUtils.setProperty(newObject, name, value);
                }
            }else {
                throw new IllegalStateException("Unexpected value: " + field.getType());
            }
        }

        return newObject;
    }

    /**
     * getFieldsByObject
     *
     * @param object
     * @param <T>
     * @return
     */
    public static <T extends BaseEntity> Map<String, Field> getFieldsByObject(T object) {
        return getFieldsByClass(object.getClass());
    }

    /**
     * 通过反射获取对应class的所有Field，并且缓存
     *
     * @param klass
     * @param <T>
     * @return
     */
    public static <T extends BaseEntity> Map<String, Field> getFieldsByClass(Class klass) {
        if (fieldsCache.containsKey(klass)) {
            return fieldsCache.get(klass);
        }
        Map<String, Field> fieldMap = internalGetFieldsByClass(klass);
        fieldsCache.put(klass, fieldMap);
        return fieldMap;
    }

    /**
     * 递归方式获取完整的字段列表
     *
     * @param klass
     * @param <T>
     */
    private static <T extends BaseEntity> Map<String, Field> internalGetFieldsByClass(Class klass) {
        if (klass == null || klass == Object.class) {
            return null;
        }
        Map<String, Field> map = new HashMap<>();

        for (Field field : klass.getDeclaredFields()) {
            map.put(field.getName(), field);
        }
        Map<String, Field> nestedMap = internalGetFieldsByClass(klass.getSuperclass());
        if (nestedMap != null) {
            map.putAll(nestedMap);
        }
        return map;
    }

    public static void fetchReferenceFields(Class<? extends BaseEntity> entityClass) {

        Field[] fields = entityClass.getDeclaredFields();
        Arrays.asList(fields).stream()
                .filter(field -> BaseEntity.class.isAssignableFrom(field.getType()));
    }

    /**
     * gentOneToManyReferenceFields
     *
     * @param entityClass
     * @return
     */
    public static Map<String, ReferenceFieldDto> getOneToManyReferenceFields(Class<? extends BaseEntity> entityClass) {
        if (manyReferenceCache.containsKey(entityClass)) {
            return manyReferenceCache.get(entityClass);
        }

        Map<String, ReferenceFieldDto> referenceFields = new HashMap<>();
        List<Field> fields = Arrays.asList(entityClass.getDeclaredFields());
        fields.forEach(field -> {
            OneToMany[] otms = field.getAnnotationsByType(OneToMany.class);
            for (OneToMany oneToMany : otms) {
                ReferenceFieldDto dto = new ReferenceFieldDto(field, oneToMany.mappedBy());
                referenceFields.put(field.getName(), dto);
            }
        });
        manyReferenceCache.put(entityClass, referenceFields);
        return referenceFields;
    }

    /**
     * gentOneToManyReferenceFields
     *
     * @param entityClass
     * @return
     */
    public static Map<String, ReferenceFieldDto> getOneToOneReferenceFields(Class<? extends BaseEntity> entityClass) {
        if (oneReferenceCache.containsKey(entityClass)) {
            return oneReferenceCache.get(entityClass);
        }

        Map<String, ReferenceFieldDto> referenceFields = new HashMap<>();
        List<Field> fields = Arrays.asList(entityClass.getDeclaredFields());
        fields.forEach(field -> {
            OneToOne[] oneToOnes = field.getAnnotationsByType(OneToOne.class);
            for (OneToOne oneToOne : oneToOnes) {
                ReferenceFieldDto dto = new ReferenceFieldDto(field, oneToOne.mappedBy());
                referenceFields.put(field.getName(), dto);
            }
        });
        oneReferenceCache.put(entityClass, referenceFields);
        return referenceFields;
    }

    /**
     * fillUp reference
     *
     * @param data
     * @param <T>
     */
    public static <T extends BaseEntity> void fillUpReference(T data) {
        Class<? extends BaseEntity> entityClass = data.getClass();
        Map<String, ReferenceFieldDto> refFields = EntityUtility.getOneToManyReferenceFields(entityClass);
        refFields.entrySet().forEach(entry -> {
            try {
                ReferenceFieldDto dto = entry.getValue();
                Collection<BaseEntity> value = (Collection<BaseEntity>) PropertyUtils.getProperty(data, entry.getKey());
                if (value != null) {
                    value.forEach(item -> {
                        try {
                            PropertyUtils.setProperty(item, dto.getMappedBy(), data);
                            fillUpReference(item);
                        } catch (Exception ex) {
                            log.warn("unknown property {} for {} reason: {}",
                                    entry.getKey(),
                                    dto.getMappedBy(),
                                    ex.getMessage()
                            );
                        }
                    });
                }
            } catch (Exception ex) {
                log.warn("unknown property {} {}", entry.getKey(), ex.getMessage());
            }
        });

        Map<String, ReferenceFieldDto> oneFields = EntityUtility.getOneToOneReferenceFields(entityClass);
        oneFields.entrySet().forEach(entry -> {
            try {
                ReferenceFieldDto dto = entry.getValue();
                BaseEntity value = (BaseEntity) PropertyUtils.getProperty(data, entry.getKey());
                if (value != null) {
                    try {
                        if (!dto.getMappedBy().isEmpty()) {
                            PropertyUtils.setProperty(value, dto.getMappedBy(), data);
                        }
                        fillUpReference(value);
                    } catch (Exception ex) {
                        log.warn("unknown property {} for {} reason: {}",
                                entry.getKey(),
                                dto.getMappedBy(),
                                ex.getMessage()
                        );
                    }
                }
            } catch (Exception ex) {
                log.warn("unknown property {} {}", entry.getKey(), ex.getMessage());
            }
        });
    }

    /**
     * mergeEntityWithData
     *
     * @param object
     * @param data
     * @param <T>
     */
    public static <T extends BaseEntity> void mergeEntityWithData(T object, Map<String, Object> data) {
        mergeEntityWithData(object, data, (f, m) -> null);
    }

    /**
     * mergeEntityWithData
     *
     * @param object
     * @param data
     * @param <T>
     */
    public static <T extends BaseEntity> void mergeEntityWithData(T object, Map<String, Object> data,
                                                                  BiFunction<Long, Class<?>, Object> getSubEntity) {
        if (object == null || data.isEmpty()) {
            return;
        }
        ConversionService conversionService = new DefaultConversionService();

        Map<String, Field> fieldMap = getFieldsByObject(object);
        data.forEach((key, value) -> {
            if (fieldMap.containsKey(key)) {
                Field field = fieldMap.get(key);
                try {
                    if (EXINCLUDE_COLUMN.contains(key)) {
                        return;
                    }
                    if (Enum.class.isAssignableFrom(field.getType())) {
                        Enum anEnum = Enum.valueOf((Class<Enum>) field.getType(), value.toString());
                        PropertyUtils.setProperty(object, key, anEnum);
                    } else if (value instanceof Map) {
                        Embedded embedded = field.getAnnotation(Embedded.class);
                        if (embedded != null || BaseDTO.class.isAssignableFrom(field.getType())) {
                            Map<String, Object> embeddedVal = (Map<String, Object>) value;
                            Object holder = PropertyUtils.getProperty(object, key);
                            embeddedVal.forEach((eKey, eValue) -> {
                                try {
                                    eValue = conversionService.convert(eValue, holder.getClass().getDeclaredField(eKey).getType());
                                    PropertyUtils.setProperty(holder, eKey, eValue);
                                } catch (Exception ex) {
                                    log.warn("Failed to update field {} for resource {}", key, object.getClass().getSimpleName());
                                }
                            });
                        } else if (BaseEntity.class.isAssignableFrom(field.getType())) {
                            Map<Object, Object> map = (Map<Object, Object>) value;
                            Object subIds = map.get("id");
                            if (subIds != null && NumberUtils.isDigits(subIds.toString())) {
                                Long subId = Long.parseLong(subIds.toString());
                                BaseEntity sub = (BaseEntity) PropertyUtils.getProperty(object, key);
                                if (sub != null && subId.equals(sub.getId())) {
                                    // ignore 相同的entity，不处理
                                } else {
                                    // entity 不同，通过id查出新的，然后替换
                                    sub = (BaseEntity) getSubEntity.apply(subId, field.getType());
                                    if (sub != null) {
                                        PropertyUtils.setProperty(object, key, sub);
                                    }
                                }
                            }
                        } else {
                            value = conversionService.convert(value, field.getType());
                            PropertyUtils.setProperty(object, key, value);
                        }
                    } else if (value instanceof List) {
                        ParameterizedType fieldType = (ParameterizedType) field.getGenericType();
                        // ignore for reference entity case, only deal basic type
                        Type actualType = fieldType.getActualTypeArguments()[0];
                        if (actualType instanceof Class) {
                            // 如果基础类型, List<Long, Int>之类的
                            Class<?> argClass = (Class<?>) actualType;
                            List<Object> newValue = ((List<?>) value).stream().map(i -> ParamsHelper.castFun(argClass).apply(i)).filter(Objects::nonNull).collect(Collectors.toList());
                            PropertyUtils.setProperty(object, key, newValue);
                        } else {
                            PropertyUtils.setProperty(object, key, value);
                        }
                    } else if (field.getType().equals(Date.class)) {
                        if (value != null) {
                            LocalDateTime date = LocalDateTime.parse(value.toString(), DATE_TIME_FORMATTER);
                            value = Date.from(date.atZone(ZoneId.systemDefault()).toInstant());
                        }
                        PropertyUtils.setProperty(object, key, value);
                    } else {
                        value = conversionService.convert(value, field.getType());
                        PropertyUtils.setProperty(object, key, value);
                    }
                } catch (Exception ex) {
                    throw new BadRequestException(String.format("Failed to update field %s for resource %s with %s", key, object.getClass().getSimpleName(), ex.getMessage()));
                }
            } else {
                log.warn("unexpected field {} just ignored...", key);
            }
        });
    }

    /**
     * @param type 如果type == Long.class 则直接返回，如果是Entity则通过id查询出来
     */
    @SuppressWarnings("unchecked")
    public static Object requireEntityOrEntityId(CrudService crudService, long id, Class<?> type) {
        if (BaseEntity.class.isAssignableFrom(type)) {
            Class<? extends BaseEntity> entityClass = (Class<? extends BaseEntity>) type;
            ResourceRepository<?, Long> repository = crudService.getRepository(entityClass);
            return repository.findById(id).orElseThrow(() -> new EntityNotFoundException(type)); // 必须存在entity
        } else {
            return id;
        }
    }

    /**
     * 设置第二层entity的parent值为value1
     */
    public static void setProperty(Object value1, Collection<EmbeddedFieldContext> contexts) {
        for (EmbeddedFieldContext context : contexts) {
            setParentProperty(value1, context.embeddedFieldInRoot, context.rootFieldInEmbedded, context.relationType, value2 -> {
                for (DeepEmbeddedFieldContext deepContext : context.deepRelationMaps.values()) {
                    setParentProperty(value2, deepContext.deepFieldInEmbedded, deepContext.embeddedFieldInDeep, deepContext.relationType, null);
                }
            });
        }
    }

    /**
     *
     */
    public static void setEmbeddedProperty(Object value1, Object parent, EmbeddedFieldContext context) {
        try {
            context.rootFieldInEmbedded.setAccessible(true);
            context.rootFieldInEmbedded.set(value1, parent);
            for (DeepEmbeddedFieldContext deepContext : context.deepRelationMaps.values()) {
                setParentProperty(value1, deepContext.deepFieldInEmbedded, deepContext.embeddedFieldInDeep, context.relationType, null);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public static void setDeepEmbeddedProperty(Object value1, Object parent, DeepEmbeddedFieldContext context) {
        try {
            context.embeddedFieldInDeep.setAccessible(true);
            context.embeddedFieldInDeep.set(value1, parent);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    private static void setParentProperty(Object value1, Field field1, Field field2, FieldRelationType relationType, Consumer<Object> nest) {
        if (value1 != null && field1 != null && field2 != null && relationType != null) {
            try {
                field1.setAccessible(true);
                Object value2 = field1.get(value1);
                if (value2 != null) {
                    if (relationType == ONE_TO_MANY) {
                        Collection<?> list = ((Collection<?>) value2);
                        for (Object value22 : list) {
                            field2.setAccessible(true);
                            field2.set(value22, value1);
                            Optional.ofNullable(nest).ifPresent(i -> i.accept(value22));
                        }
                    } else if (relationType == ONE_TO_ONE) {
                        field2.setAccessible(true);
                        field2.set(value2, value1);
                        Optional.ofNullable(nest).ifPresent(i -> i.accept(value2));
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
                throw new BadRequestException();
            }
        }
    }
}
