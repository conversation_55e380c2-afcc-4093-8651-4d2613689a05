package org.befun.core.entity.annotation;

import org.befun.core.constant.EntityScopeStrategyType;
import org.hibernate.annotations.ParamDef;

import java.lang.annotation.*;

/**
 * 标记数据权限类型
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Documented
public @interface EntityCustomStrategy {

    String name();

    EntityScopeStrategyType value();

    String defaultCondition() default "";

    String resource() default "";

    ParamDef[] parameters() default {};
}
