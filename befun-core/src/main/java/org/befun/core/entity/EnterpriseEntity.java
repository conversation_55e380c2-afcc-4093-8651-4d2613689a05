package org.befun.core.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

import static org.befun.core.constant.EntityFilters.*;

@MappedSuperclass
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor

@FilterDef(
        name = shareFilter,
        parameters = {
                @ParamDef(name = "orgId", type = "long")
        },
        defaultCondition = "(org_id = :orgId or org_id is null)")
@Filter(name = shareFilter)

@FilterDef(
        name = organizationFilter,
        parameters = {
                @ParamDef(name = "orgId", type = "long")
        },
        defaultCondition = "org_id = :orgId")
@Filter(name = organizationFilter)

@FilterDef(
        name = orgExcludeOwnerFilter,
        parameters = {
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long")
        },
        defaultCondition = "org_id = :orgId and (user_id != :userId or user_id is null)")
@Filter(name = orgExcludeOwnerFilter)

@FilterDef(
        name = departmentFilter,
        parameters = {
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "subDepartmentIds", type = "long")
        },
        defaultCondition = "org_id = :orgId and (department_id in (:subDepartmentIds) or department_id is null)")
@Filter(name = departmentFilter)

@FilterDef(
        name = ownerFilter,
        parameters = {
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long")
        },
        defaultCondition = "org_id = :orgId and user_id = :userId")
@Filter(name = ownerFilter)

@FilterDef(
        name = corpFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")},
        defaultCondition = "org_id = :orgId and user_id != :userId and id in " + resourceIdsSql)
@Filter(name = corpFilter)

@FilterDef(
        name = ownerCorpFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")},
        defaultCondition = "org_id = :orgId and (user_id = :userId or id in " + resourceIdsSql + ")")
@Filter(name = ownerCorpFilter)

@FilterDef(
        name = groupCorpFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "groupResource", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")
        },
        defaultCondition = "org_id = :orgId and user_id != :userId and (id in " + resourceIdsSql + " or group_id in " + resourceGroupIdsSql + ")")
@Filter(name = groupCorpFilter)

@FilterDef(
        name = ownerGroupCorpFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "groupResource", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")
        },
        defaultCondition = "org_id = :orgId and (user_id = :userId or id in " + resourceIdsSql + " or group_id in " + resourceGroupIdsSql + ")")
@Filter(name = ownerGroupCorpFilter)

@FilterDef(
        name = ownerFolderCorpFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")
        },
        defaultCondition = "org_id = :orgId and (user_id = :userId or id in " + resourceIdsSql + " or parent_id in " + resourceIdsSql + ")")
@Filter(name = ownerFolderCorpFilter)

@FilterDef(
        name = ownerCorpDepartmentUsersFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long"),
                @ParamDef(name = "userIds", type = "long")
        },
        defaultCondition = "org_id=:orgId and (user_id in (:userIds) or id in " + resourceIdsSql + ")")
@Filter(name = ownerCorpDepartmentUsersFilter)

@FilterDef(
        name = ownerGroupCorpDepartmentUsersFilter,
        parameters = {
                @ParamDef(name = "type", type = "string"),
                @ParamDef(name = "groupResource", type = "string"),
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long"),
                @ParamDef(name = "userIds", type = "long")
        },
        defaultCondition = "org_id=:orgId and (user_id in (:userIds) or id in " + resourceIdsSql + " or group_id in " + resourceGroupIdsSql + ")")
@Filter(name = ownerGroupCorpDepartmentUsersFilter)

@EntityListeners(EnterpriseListener.class)
public abstract class EnterpriseEntity extends BaseEntity implements EnterpriseAware {
    @Column(name = "org_id")
    @DtoProperty(description = "orgId", jsonView = ResourceViews.Basic.class)
    public Long orgId;
}
