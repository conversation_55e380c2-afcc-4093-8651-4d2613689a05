package org.befun.core.constant;

import lombok.Getter;
import lombok.NoArgsConstructor;

import static org.befun.core.constant.EntityFilters.*;

@Getter
@NoArgsConstructor
public enum EntityScopeStrategyType implements EntityScopeStrategyTypes {
    SHARE(shareFilter),                               // 整个企业和全局共享
    ORGANIZATION(organizationFilter),                 // 整个企业共享，默认策略
    ORGANIZATION_EXCLUDE_OWNER(orgExcludeOwnerFilter),// 整个企业，不包括自己，超级管理员查询与我共享数据时，使用此策略
    OWNER(ownerFilter),                               // 只有创建者才能访问 对应：ownerFilter
    CORPORATION(corpFilter),                          // 只有邀请协作才能访问
    GROUP_CORPORATION(groupCorpFilter),               // 只有邀请协作(包括协作的目录里的资源)才能访问
    OWNER_CORPORATION(ownerCorpFilter),               // 只有创建者或者邀请协作才能访问
    OWNER_GROUP_CORPORATION(ownerGroupCorpFilter),    // 只有创建者或者邀请协作(包括协作的目录里的资源)才能访问
    OWNER_FOLDER_CORPORATION(ownerFolderCorpFilter),  // 只有创建者或者邀请协作才能访问, 邀请可以设置在文件夹级别 （配合TreeEntity）
    DEPARTMENT(departmentFilter),                     // 当前部门以及下属部门可以访问
    OWNER_CORPORATION_DEPARTMENT_USERS(ownerCorpDepartmentUsersFilter),          // 属于当前部门及下属部门用户的可以访问
    OWNER_GROUP_CORPORATION_DEPARTMENT_USERS(ownerGroupCorpDepartmentUsersFilter),          // 属于当前部门及下属部门用户的可以访问
    NONE(none),                                       // 不使用Filter
    ;
    /* 对应EnterpriseEntity @FilterDef */
    private String filter;


    EntityScopeStrategyType(String filter) {
        this.filter = filter;
    }

}
