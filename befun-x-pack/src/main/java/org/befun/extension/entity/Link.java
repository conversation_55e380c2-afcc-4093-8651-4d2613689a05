package org.befun.extension.entity;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Entity
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class Link extends BaseEntity {
    @Column(name = "url")
    private String url;
    @Column(name = "survey_id")
    private Long surveyId;
    @Column(name = "survey_client_prefix")
    private String surveyClientPrefix;
    @Column(name = "params")
    private String params;
    @Column(name = "source") // 短链来源 1 server 2 api
    private Integer source;
    @Column(name = "external_user_id")
    private String externalUserId;

    public String getHash() {
        // 计算surveyId:params:source:surveyClientPrefix的hash值
        try {
        String str = String.format("%d:%s:%d:%s", surveyId, params, source, surveyClientPrefix);
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(str.getBytes());
        byte[] digest = md.digest();
        BigInteger bi = new BigInteger(1, digest);
        return String.format("%0" + (digest.length << 1) + "x", bi);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
}
