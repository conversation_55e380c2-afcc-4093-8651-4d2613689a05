package org.befun.extension.repository;


import org.befun.extension.entity.Link;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LinkRepository extends CrudRepository<Link, Long> {
    Optional<Link> findFirstByUrl(String url);
    int countBySurveyIdAndExternalUserId(Long surveyId, String externalUserId);
}
