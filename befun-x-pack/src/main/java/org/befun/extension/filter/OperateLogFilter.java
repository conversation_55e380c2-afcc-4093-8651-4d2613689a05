package org.befun.extension.filter;

import cn.hanyi.common.ip.resolver.IpResolverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpStatus;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.context.TenantData;
import org.befun.core.utils.AESUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.ListHelper;
import org.befun.core.utils.RestUtils;
import org.befun.extension.Extensions;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.OperateLogConfigDto;
import org.befun.extension.dto.XpackOperateLogConfigDto;
import org.befun.extension.entity.OperateLog;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.property.XPackFilterProperty;
import org.befun.extension.repository.OperateLogRepository;
import org.befun.extension.service.XpackConfigService;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * RequestBodyAdvice  存在请求中清空了TenantContext，导致后续的TenantContext获取不到
 * ResponseBodyAdvice 用与获取记录请求后的数据
 */
@Slf4j
@Order(XPackFilter.ORDER_OPERATE_LOG)
@Component("xPackOperateLogFilter")
@ConditionalOnProperty(name = Extensions.OPERATE_LOG_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.OPERATE_LOG_MATCH_IF_MISSING)
public class OperateLogFilter implements WebInternalFilter {

    @Autowired
    private IpResolverService ipResolverService;

    @Autowired
    private OperateLogRepository operateLogRepository;

    @Autowired
    private XpackConfigService xpackConfigService;

    @Autowired
    private XPackFilterProperty xPackFilterProperty;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private EntityManager entityManager;

    public static final String KEY = "cache:xpack-config:log";
    public static final String KEY_LOCK = "cache:xpack-config:log-lock";
    private static final String HK_STATUS = "log-status";
    private static final String HK_STATUS_ENABLE = "enable";
    private static final String HK_STATUS_DISABLE = "disable";

    @Override
    public void postFilter(XPackFilterContext context) {
        try {
            log(context);
        } catch (Throwable e) {
            log.error("日志管理：写入日志失败，忽略错误{}", e.getMessage());
        }
    }

    public void log(XPackFilterContext context) {
        var httpData = RequestInfoContext.get();
        if (httpData == null) {
            log.error("日志管理：未解析出请求参数");
            return;
        }
        var urlPath = context.getReplacedPath(xPackFilterProperty.getPathReplaceRules());
        var method = context.getUpperCaseMethod();

        var configKey = String.format("%s:%s", method, urlPath);
        var record = getLogConfig(configKey);

        if (record != null) {

            var operate = new OperateLog();
            var ip = RestUtils.getClientIpAddress(context.getRequest());
            var regionInfo = ipResolverService.resolveIpToRegion(ip);

            TenantData tenantData = httpData.getTenantData();
            if (tenantData != null) {
                operate.setOrgId(tenantData.getOrgId());
                operate.setUserId(tenantData.getUserId());
                operate.setDepartmentIds(ListHelper.formatDepartmentIds(tenantData.getDepartmentIds()));
            }

            operate.setIp(ip);
            operate.setCountry(regionInfo.getCountry());
            operate.setProvince(regionInfo.getProvince());
            operate.setCity(regionInfo.getCity());
            operate.setResult(calcResult(record, context.getResponse().getStatus(), httpData));
            operate.setUrl(context.getFullUrl());
            operate.setParams(encryptParams(httpData.getRequestBody(), tenantData.getOrgId()));
            operate.setContent(context.getWrapperResponse().getResponseString());
            operate.setModule(record.getModule());
            operate.setAction(record.getAction());

            operateLogRepository.save(operate);
        }
    }

    private String encryptParams(String params, Long orgId) {
        AtomicReference<String> result = new AtomicReference<>(params);
        try {
            String sql = "SELECT k.api_secret FROM api_key k JOIN user u ON k.user_id=u.id WHERE u.is_admin=1 and k.org_id=:orgId limit 1";
            Session session = entityManager.unwrap(Session.class);
            NativeQuery query = session.createSQLQuery(sql);
            query.setParameter("orgId", orgId);
            Optional.ofNullable(query.getSingleResult()).ifPresent(s -> {
                try {
                    result.set(AESUtils.encrypt(s.toString(), params));
                } catch (Exception ignored) {
                }
            });
        } catch (Exception ignored) {
        }

        return result.get();
    }

    private boolean calcResult(OperateLogConfigDto config, int httpStatus, RequestInfoContext.Data httpData) {
        if (!List.of(HttpStatus.SC_OK, HttpStatus.SC_MOVED_TEMPORARILY).contains(httpStatus)) {
            return false;
        }
        if (config.getCode() != null) {
            if (httpData.getOriginResponse() == null || !(httpData.getOriginResponse() instanceof BaseResponseDto)) {
                return false;
            } else {
                BaseResponseDto<?> response = (BaseResponseDto<?>) httpData.getOriginResponse();
                if (config.getCode() != response.getCode()) {
                    return false;
                }
            }
        }
        if (config.getInternalCode() != null) {
            if (httpData.getOriginResponse() == null || !(httpData.getOriginResponse() instanceof BaseResponseDto)) {
                return false;
            } else {
                BaseResponseDto<?> response = (BaseResponseDto<?>) httpData.getOriginResponse();
                if (config.getInternalCode() != response.getInternalCode()) {
                    return false;
                }
            }
        }
        return true;
    }

    private OperateLogConfigDto getLogConfig(String hashKey) {
        Object status = stringRedisTemplate.opsForHash().get(KEY, HK_STATUS);
        if (status == null) {
            return syncCache(hashKey);
        } else if (status.toString().equals(HK_STATUS_DISABLE)) {
            return null;
        }

        Object info = stringRedisTemplate.opsForHash().get(KEY, hashKey);
        if (info == null) {
            return null;
        }
        return JsonHelper.toObject(info.toString(), OperateLogConfigDto.class);
    }

    private OperateLogConfigDto syncCache(String hashKey) {
        var optionalXpackConfig = xpackConfigService.findAllEntities().stream().filter(x -> XPackAppType.OPERATE_LOG.equals(x.getType())).findFirst();
        XpackConfig xpackConfig;
        Map<String, String> cache = new HashMap<>();
        String status = HK_STATUS_DISABLE;
        OperateLogConfigDto operateLogConfig = null;
        if (optionalXpackConfig.isPresent()
                && (xpackConfig = optionalXpackConfig.get()).getEnabled() != null
                && xpackConfig.getEnabled()) {
            var xpackConfigDto = JsonHelper.toObject(xpackConfig.getConfig(), XpackOperateLogConfigDto.class);
            if (xpackConfigDto != null && MapUtils.isNotEmpty(xpackConfigDto.getOprateLogConfig())) {
                status = HK_STATUS_ENABLE;
                for (Map.Entry<String, OperateLogConfigDto> e : xpackConfigDto.getOprateLogConfig().entrySet()) {
                    cache.put(e.getKey(), JsonHelper.toJson(e.getValue()));
                    if (operateLogConfig == null && hashKey.equals(e.getKey())) {
                        operateLogConfig = e.getValue();
                    }
                }
            }
        }
        cache.put(HK_STATUS, status);
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(KEY_LOCK, "1", Duration.ofSeconds(10));
        if (lock != null && lock) {
            stringRedisTemplate.opsForHash().putAll(KEY, cache);
        }
        return operateLogConfig;
    }

    public void clearCache() {
        stringRedisTemplate.delete(List.of(KEY, KEY_LOCK));
    }

    public static void clearCache(StringRedisTemplate stringRedisTemplate) {
        stringRedisTemplate.delete(List.of(KEY, KEY_LOCK));
    }

}
