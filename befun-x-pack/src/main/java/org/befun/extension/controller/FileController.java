package org.befun.extension.controller;

import cn.hanyi.common.file.storage.FileStorageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.utils.DateHelper;
import org.befun.extension.Extensions;
import org.befun.extension.dto.FileDto;
import org.befun.extension.dto.FileUploadDto;
import org.befun.extension.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;


@Slf4j
@Tag(name = "文件上传")
@RestController
@ConditionalOnProperty(name = Extensions.UPLOAD_FILE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.UPLOAD_FILE_MATCH_IF_MISSING)
@ConditionalOnBean({FileService.class, FileStorageService.class})
public class FileController {

    @Autowired
    private FileStorageService storageService;

    @Autowired
    private FileService fileService;

    @Autowired
    private LegacyAuthTokenFilter legacyAuthTokenFilter;

    private final Map<String, byte[]> fixedMap = Collections.synchronizedMap(new LinkedHashMap<String, byte[]>(10, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, byte[]> eldest) {
            return size() > 10;
        }
    });

    @SneakyThrows
    @RequestMapping(value = "/files", method = RequestMethod.POST)
    public ResourceResponseDto<String> upload(
            @RequestParam("file") MultipartFile file,
            FileUploadDto uploadDto
    ) {
        return new ResourceResponseDto<>(fileService.upload(file, uploadDto, null, false).getUrl());
    }

    @RequestMapping(value = "/files-token", method = RequestMethod.POST)
    public ResourceResponseDto<String> uploadWithToken(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "token", required = false) String token,
            @RequestParam(value = "useFileName", required = false) Boolean useFileName,
            @RequestParam(value = "path", required = false
            ) String path
    ) {
        return new ResourceResponseDto<>(fileService.uploadWithToken(file, path, token, useFileName).getUrl());
    }

    @RequestMapping(value = "/files", method = RequestMethod.GET)
    public void download(HttpServletRequest request, HttpServletResponse response, @Valid FileDto params) throws IOException {

        if(params.getUrl().startsWith(storageService.getFileStorage().getBasePath() + FileService.PRIVATE_PATH)){
            Optional.ofNullable(params.getToken()).orElseThrow( ()-> new BadRequestException("token is required"));
            Optional.ofNullable(legacyAuthTokenFilter.fetchUserInfoByToken(params.getToken())).orElseThrow(() -> new BadRequestException("token is invalid"));
        }

        String url = params.getUrl();
        String fixedMapKey = DateHelper.formatDate(new Date()) + url;

        // 使用compute方法，确保原子性的检索或计算值
//        byte[] bytes = fixedMap.compute(fixedMapKey, (key, existingValue) -> {
//            if (existingValue == null) {
//                return fileService.download(url, fileName -> response.setHeader("Content-Disposition", "attachment; filename=" + fileName));
//            } else {
//                return existingValue;
//            }
//        });

        byte[] bytes =  fileService.download(url, fileName -> response.setHeader("Content-Disposition", "attachment; filename=" + fileName));

        int start = 0;
        int totalSize = bytes.length;
        int readSize = bytes.length;

        if (request.getHeader("Range") != null) {

            response.setStatus(206);
            String[] range = request.getHeader("Range").replace("bytes=", "").split("-");
            start = Integer.valueOf(range[0]);
            int end = range.length > 1 ? Integer.valueOf(range[1]) : readSize - 1;
            readSize = (end - start) + 1;
            response.setContentLength(readSize);
            response.setHeader("Content-Type", "video/mp4");
            response.setHeader("content-range", String.format("bytes %s-%s/%s", start, end, totalSize));
        }

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(bytes, start, readSize);
            outputStream.flush();
        } catch (IOException ignored) {
        }

    }
}
