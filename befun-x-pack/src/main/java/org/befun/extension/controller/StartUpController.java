package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantContext;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.http.HttpRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.resource.HttpResource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

@Hidden
@RestController
public class StartUpController implements ApplicationRunner {

    private final AtomicBoolean startUp = new AtomicBoolean();

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Hidden
    @Operation(hidden = true)
    @RequestMapping("start-up-status")
    public boolean startUpStatus(HttpServletRequest request) {
        if(StringUtils.isNotEmpty(request.getHeader("x-api-key"))) {
            if(TenantContext.getCurrentTenant() == null){
                throw new AuthenticationCredentialsNotFoundException("x-api-key不合法");
            }
        }
        return startUp.get();
    }
    @Override
    public void run(ApplicationArguments args) throws Exception {
        startUp.compareAndSet(false, true);
    }

    @SneakyThrows
    @GetMapping("/create-time")
    public Object version() {
        ApplicationHome ah = new ApplicationHome();
        File source = ah.getSource();
        if (source != null) {
            BasicFileAttributes attrs = Files.readAttributes(source.toPath(), BasicFileAttributes.class);
            FileTime time = attrs.creationTime();
            String createTime = String.format("%s Creation time: %s%n", source.getName(), time);
            System.out.printf(createTime);
            return createTime;
        }
        return null;
    }


}
