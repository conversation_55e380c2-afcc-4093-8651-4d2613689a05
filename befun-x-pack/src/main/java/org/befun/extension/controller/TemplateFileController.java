package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.Extensions;
import org.befun.extension.dto.TemplateFileDto;
import org.befun.extension.property.TemplateFileProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "模板文件下载")
@RestController
@RequestMapping("template-file")
@ConditionalOnProperty(name = Extensions.TEMPLATE_FILE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.TEMPLATE_FILE_MATCH_IF_MISSING)
public class TemplateFileController {

    @Autowired
    private TemplateFileProperty templateFileProperty;
    private static final String STATIC_FOLDER = "static";

    @GetMapping
    @Operation(summary = "获得所有模板地址")
    public ResourceListResponseDto<TemplateFileDto> findAll() {
        ResourceListResponseDto<TemplateFileDto> dto = new ResourceListResponseDto<>();
        if (templateFileProperty.isEnable()) {
            dto.setItems(templateFileProperty.getItems().stream().map(i -> new TemplateFileDto(i.getType(), i.getFileUrl())).collect(Collectors.toList()));
        }
        return dto;
    }

    @GetMapping("{type}")
    @Operation(summary = "获得指定的模板地址")
    public ResourceResponseDto<TemplateFileDto> findOne(@PathVariable String type) {
        TemplateFileDto template = null;
        if (templateFileProperty.isEnable()) {
            template = templateFileProperty.getItems()
                    .stream()
                    .filter(i -> type.equals(i.getType()))
                    .findFirst()
                    .map(i -> new TemplateFileDto(i.getType(), i.getFileUrl()))
                    .orElse(null);
        }
        if (template == null) {
            throw new BadRequestException("模板不存在");
        } else {
            return new ResourceResponseDto<>(template);
        }
    }

    @GetMapping("download/{type}")
    @Operation(summary = "下载指定的模板")
    public void downloadOne(@PathVariable String type, HttpServletResponse response) throws IOException {
        if (templateFileProperty.isEnable()) {
            TemplateFileProperty.TemplateFileItem fileItem = templateFileProperty.getItems()
                    .stream()
                    .filter(i -> type.equals(i.getType())).findFirst()
                    .orElse(null);
            
            if(fileItem != null && fileItem.getFileUrl() !=null){
                String file = fileItem.getFileUrl();
                if (file.startsWith("http") && StringUtils.isNotEmpty(file)) {
                    response.sendRedirect(file);
                    return;
                }else{
                    Resource resource = new ClassPathResource(STATIC_FOLDER + "/" + file);

                    if (!resource.exists()) {
                        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        throw new BadRequestException("模板不存在");
                    }

                    String filename = Objects.requireNonNull(resource.getFilename());
                    String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replace("+", "%20");

                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename);

                    try (InputStream inputStream = resource.getInputStream();
                         OutputStream outputStream = response.getOutputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                        outputStream.flush();
                        return; // Successfully processed the file, so return
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        // Only throw the exception if we didn't find a template
        throw new BadRequestException("模板不存在");
    }

}
