server:
  port: ${PORT:8082}
  error:
    whitelabel:
      enabled: false

spring:
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:***********************************}
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:20210819yym}
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  jpa:
    generate-ddl: false
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5Dialect
  data:
    rest:
      default-media-type: application/json
  jackson:
    date-format: com.fasterxml.jackson.databind.util.ISO8601DateFormat

logging:
  level:
    root: ${LOG_LEVEL:info}
    org:
      hibernate:
        SQL: debug
        type: trace


befun:
  server:
    enable-open-api-filter: true
  extension:
    shorturl:
      root: ${ROOT_URL:http://localhost:8080}
    wechat-open:
      enable: true
      component-app-id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
      component-secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
      component-token: ${WECHAT_OPEN_TOKEN:surveyplus}
      component-aes-key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    wechat-mp:
      enable: true
      app-id: ${WECHAT_MP_CEM_APP_ID:wxa3a9422f58e65a48}
      app-secret: ${WECHAT_MP_CEM_APP_SECRET:74794b288b734d7a43db94cdeb8acfa7}
      token: ${WECHAT_MP_CEM_TOKEN:hanyidata}
    smart-verify:
      enable-smart-verify: true
      regionId: cn-hangzhou
      accessKeyId: LTAI4G5RfyxPtajMojKJPvmM
      accessKeySecret: ******************************
      product: afs
      domain: afs.aliyuncs.com
      appKey: FFFF0N00000000008B36
    toast-message:
      "[]":
    sms:
      vendor: activemq
      enable-activemq: true
      providers:
        - name: activemq
          activemq:
            queue: test-sms
            date-time-formatter: yyyyMMddHHmmss
            send-and-receive: false
            send-text:
              '
              <?xml version="1.0" encoding="UTF-8?>
              <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
                <xs:element name="shortmsg">
                  <xs:annotation>
                    <xs:documentation>短信报文</xs:documentation>
                  </xs:annotation>
                  <xs:complexType>
                    <xs:sequence>
                      <xs:element name="channel" type="xs:string"/>
                      <xs:element name="channeldate" type="xs:dateTime"/>
                      <xs:element name="id" type="xs:string"/>
                      <xs:element name="telnumber" type="xs:string"/>
                      <xs:element name="content" type="xs:string"/>
                      <xs:element name="thddate" type="xs:dateTime"/>
                      <xs:element name="khjl" type="xs:string"/>
                      <xs:element name="smsgy" type="xs:string"/>
                      <xs:element name="smsjg" type="xs:string"/>
                      <xs:element name="account" type="xs:string"/>
                      <xs:element name="cardid" type="xs:string"/>
                      <xs:element name="userid" type="xs:string"/>
                      <xs:element name="advflag" type="xs:string"/>
                      <xs:element name="priority" type="xs:string"/>
                      <xs:element name="feetype" type="xs:string"/>
                      <xs:element name="reserve1" type="xs:string"/>
                      <xs:element name="reserve2" type="xs:string"/>
                      <xs:element name="reserve3" type="xs:string"/>
                    </xs:sequence>
                  </xs:complexType>
                </xs:element>
              </xs:schema 
              '
          templates:
            - name: default
              content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！


#hanyi:
#  shorturl:
#    root: ${ROOT_URL:http://localhost:8080}

hanyi:
  common:
    ip-resolver:
      default-platform: local
      local:
        algorithm: memory
    file-storage:
      default-platform: default
      local:
        - platform: default # 存储平台标识
          enable-storage: true
          enable-access: true
          domain: "http://localhost:8080/tmp/files/"
          base-path: /tmp/files/
          path-patterns: /tmp/files/**




